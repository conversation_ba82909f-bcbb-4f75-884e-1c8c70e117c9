<?php

use App\Models\Author;
use App\Models\Book;
use App\Models\Chapter;
use App\Models\Publisher;
use App\Models\User;

beforeEach(function () {
    $this->actingAs(User::factory()->create());
});

test('can view books index', function () {
    $publisher = Publisher::factory()->create();
    $books = Book::factory(3)->create(['publisher_id' => $publisher->id]);

    $response = $this->get(route('books.index'));

    $response->assertStatus(200);
    $response->assertViewIs('books.index');
    $response->assertViewHas('books');

    foreach ($books as $book) {
        $response->assertSee($book->title);
    }
});

test('can view book create form', function () {
    Publisher::factory()->create();
    Author::factory()->create();

    $response = $this->get(route('books.create'));

    $response->assertStatus(200);
    $response->assertViewIs('books.create');
    $response->assertViewHas(['publishers', 'authors']);
});

test('can create book with valid data', function () {
    $publisher = Publisher::factory()->create();
    $authors = Author::factory(2)->create();

    $bookData = [
        'title' => 'Test Book',
        'isbn' => '978-0123456789',
        'description' => 'This is a test book description.',
        'publication_date' => '2023-01-01',
        'publisher_id' => $publisher->id,
        'authors' => $authors->pluck('id')->toArray(),
    ];

    $response = $this->post(route('books.store'), $bookData);

    $response->assertRedirect();
    $this->assertDatabaseHas('books', [
        'title' => 'Test Book',
        'isbn' => '978-0123456789',
        'publisher_id' => $publisher->id,
    ]);

    $book = Book::where('title', 'Test Book')->first();
    $response->assertRedirect(route('books.show', $book));

    // Check many-to-many relationship
    $this->assertEquals(2, $book->authors()->count());
    foreach ($authors as $author) {
        $this->assertTrue($book->authors->contains($author));
    }
});

test('can create book without authors', function () {
    $publisher = Publisher::factory()->create();

    $bookData = [
        'title' => 'Book Without Authors',
        'publisher_id' => $publisher->id,
    ];

    $response = $this->post(route('books.store'), $bookData);

    $response->assertRedirect();
    $this->assertDatabaseHas('books', $bookData);

    $book = Book::where('title', 'Book Without Authors')->first();
    $this->assertEquals(0, $book->authors()->count());
});

test('cannot create book without required fields', function () {
    $response = $this->post(route('books.store'), []);

    $response->assertSessionHasErrors(['title', 'publisher_id']);
    $this->assertDatabaseCount('books', 0);
});

test('cannot create book with duplicate isbn', function () {
    $publisher = Publisher::factory()->create();
    Book::factory()->create(['isbn' => '978-0123456789', 'publisher_id' => $publisher->id]);

    $bookData = [
        'title' => 'Duplicate ISBN Book',
        'isbn' => '978-0123456789',
        'publisher_id' => $publisher->id,
    ];

    $response = $this->post(route('books.store'), $bookData);

    $response->assertSessionHasErrors(['isbn']);
});

test('cannot create book with nonexistent publisher', function () {
    $bookData = [
        'title' => 'Test Book',
        'publisher_id' => 999,
    ];

    $response = $this->post(route('books.store'), $bookData);

    $response->assertSessionHasErrors(['publisher_id']);
    $this->assertDatabaseCount('books', 0);
});

test('cannot create book with nonexistent authors', function () {
    $publisher = Publisher::factory()->create();

    $bookData = [
        'title' => 'Test Book',
        'publisher_id' => $publisher->id,
        'authors' => [999, 998],
    ];

    $response = $this->post(route('books.store'), $bookData);

    $response->assertSessionHasErrors(['authors.0', 'authors.1']);
    $this->assertDatabaseCount('books', 0);
});

test('can view book details', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $authors = Author::factory(2)->create();
    $book->authors()->attach($authors);

    $response = $this->get(route('books.show', $book));

    $response->assertStatus(200);
    $response->assertViewIs('books.show');
    $response->assertViewHas('book', $book);
    $response->assertSee($book->title);
    $response->assertSee($publisher->name);
    foreach ($authors as $author) {
        $response->assertSee($author->name);
    }
});

test('can view book edit form', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);

    $response = $this->get(route('books.edit', $book));

    $response->assertStatus(200);
    $response->assertViewIs('books.edit');
    $response->assertViewHas(['book', 'publishers', 'authors']);
});

test('can update book with valid data', function () {
    $publisher = Publisher::factory()->create();
    $newPublisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $oldAuthors = Author::factory(2)->create();
    $newAuthors = Author::factory(2)->create();
    $book->authors()->attach($oldAuthors);

    $updateData = [
        'title' => 'Updated Book Title',
        'isbn' => '978-9876543210',
        'description' => 'Updated description.',
        'publication_date' => '2024-01-01',
        'publisher_id' => $newPublisher->id,
        'authors' => $newAuthors->pluck('id')->toArray(),
    ];

    $response = $this->put(route('books.update', $book), $updateData);

    $response->assertRedirect(route('books.show', $book));
    $this->assertDatabaseHas('books', [
        'id' => $book->id,
        'title' => 'Updated Book Title',
        'publisher_id' => $newPublisher->id,
    ]);

    // Check authors were synced
    $book->refresh();
    $this->assertEquals(2, $book->authors()->count());
    foreach ($newAuthors as $author) {
        $this->assertTrue($book->authors->contains($author));
    }
    foreach ($oldAuthors as $author) {
        $this->assertFalse($book->authors->contains($author));
    }
});

test('cannot update book without required fields', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);

    $response = $this->put(route('books.update', $book), ['title' => '', 'publisher_id' => '']);

    $response->assertSessionHasErrors(['title', 'publisher_id']);
});

test('can delete book', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $authors = Author::factory(2)->create();
    $book->authors()->attach($authors);

    $response = $this->delete(route('books.destroy', $book));

    $response->assertRedirect(route('books.index'));
    $this->assertDatabaseMissing('books', ['id' => $book->id]);
    $this->assertDatabaseMissing('book_author', ['book_id' => $book->id]);

    // Authors should remain
    foreach ($authors as $author) {
        $this->assertDatabaseHas('authors', ['id' => $author->id]);
    }
});

test('deleting book cascades to chapters', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapter = Chapter::factory()->create(['book_id' => $book->id]);

    $this->delete(route('books.destroy', $book));

    $this->assertDatabaseMissing('books', ['id' => $book->id]);
    $this->assertDatabaseMissing('chapters', ['id' => $chapter->id]);
});

test('unauthenticated user cannot access books', function () {
    auth()->logout();

    $response = $this->get(route('books.index'));
    $response->assertRedirect(route('login'));
});

test('returns 404 for nonexistent book', function () {
    $response = $this->get(route('books.show', 999));
    $response->assertStatus(404);
});
