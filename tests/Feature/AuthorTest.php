<?php

use App\Models\Author;
use App\Models\Book;
use App\Models\Publisher;
use App\Models\User;

beforeEach(function () {
    $this->actingAs(User::factory()->create());
});

test('can view authors index', function () {
    $authors = Author::factory(3)->create();

    $response = $this->get(route('authors.index'));

    $response->assertStatus(200);
    $response->assertViewIs('authors.index');
    $response->assertViewHas('authors');

    foreach ($authors as $author) {
        $response->assertSee($author->name);
    }
});

test('can view author create form', function () {
    $response = $this->get(route('authors.create'));

    $response->assertStatus(200);
    $response->assertViewIs('authors.create');
});

test('can create author with valid data', function () {
    $authorData = [
        'name' => 'Test Author',
        'email' => '<EMAIL>',
        'bio' => 'This is a test author biography.',
        'birth_date' => '1980-01-01 00:00:00',
    ];

    $response = $this->post(route('authors.store'), $authorData);

    $response->assertRedirect();
    $this->assertDatabaseHas('authors', data: $authorData);

    $author = Author::where('name', 'Test Author')->first();
    $response->assertRedirect(route('authors.show', $author));
});

test('can create author with minimal data', function () {
    $authorData = [
        'name' => 'Minimal Author',
    ];

    $response = $this->post(route('authors.store'), $authorData);

    $response->assertRedirect();
    $this->assertDatabaseHas('authors', $authorData);
});

test('cannot create author without required fields', function () {
    $response = $this->post(route('authors.store'), []);

    $response->assertSessionHasErrors(['name']);
    $this->assertDatabaseCount('authors', 0);
});

test('cannot create author with invalid email', function () {
    $authorData = [
        'name' => 'Test Author',
        'email' => 'invalid-email',
    ];

    $response = $this->post(route('authors.store'), $authorData);

    $response->assertSessionHasErrors(['email']);
    $this->assertDatabaseCount('authors', 0);
});

test('cannot create author with future birth date', function () {
    $authorData = [
        'name' => 'Test Author',
        'birth_date' => now()->addYear()->format('Y-m-d'),
    ];

    $response = $this->post(route('authors.store'), $authorData);

    $response->assertSessionHasErrors(['birth_date']);
    $this->assertDatabaseCount('authors', 0);
});

test('can view author details', function () {
    $author = Author::factory()->create();

    $response = $this->get(route('authors.show', $author));

    $response->assertStatus(200);
    $response->assertViewIs('authors.show');
    $response->assertViewHas('author', $author);
    $response->assertSee($author->name);
});

test('can view author edit form', function () {
    $author = Author::factory()->create();

    $response = $this->get(route('authors.edit', $author));

    $response->assertStatus(200);
    $response->assertViewIs('authors.edit');
    $response->assertViewHas('author', $author);
});

test('can update author with valid data', function () {
    $author = Author::factory()->create();
    $updateData = [
        'name' => 'Updated Author Name',
        'email' => '<EMAIL>',
        'bio' => 'Updated biography.',
        'birth_date' => '1985-05-15 00:00:00',
    ];

    $response = $this->put(route('authors.update', $author), $updateData);

    $response->assertRedirect(route('authors.show', $author));
    $this->assertDatabaseHas('authors', array_merge(['id' => $author->id], $updateData));
});

test('cannot update author without required fields', function () {
    $author = Author::factory()->create();

    $response = $this->put(route('authors.update', $author), ['name' => '']);

    $response->assertSessionHasErrors(['name']);
});

test('can delete author', function () {
    $author = Author::factory()->create();

    $response = $this->delete(route('authors.destroy', $author));

    $response->assertRedirect(route('authors.index'));
    $this->assertDatabaseMissing('authors', ['id' => $author->id]);
});

test('deleting author removes book relationships', function () {
    $author = Author::factory()->create();
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $book->authors()->attach($author);

    $this->delete(route('authors.destroy', $author));

    $this->assertDatabaseMissing('authors', ['id' => $author->id]);
    $this->assertDatabaseMissing('book_author', ['author_id' => $author->id]);
    $this->assertDatabaseHas('books', ['id' => $book->id]); // Book should remain
});

test('author shows book count', function () {
    $author = Author::factory()->create();
    $publisher = Publisher::factory()->create();
    $books = Book::factory(2)->create(['publisher_id' => $publisher->id]);
    $author->books()->attach($books);

    $response = $this->get(route('authors.index'));

    $response->assertSee('2 books');
});

test('unauthenticated user cannot access authors', function () {
    auth()->logout();

    $response = $this->get(route('authors.index'));
    $response->assertRedirect(route('login'));
});

test('returns 404 for nonexistent author', function () {
    $response = $this->get(route('authors.show', 999));
    $response->assertStatus(404);
});
