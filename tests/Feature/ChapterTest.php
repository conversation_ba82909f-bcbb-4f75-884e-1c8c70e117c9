<?php

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Publisher;
use App\Models\User;

beforeEach(function () {
    $this->actingAs(User::factory()->create());
});

test('can view chapters index', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapters = Chapter::factory(3)->create(['book_id' => $book->id]);

    $response = $this->get(route('chapters.index'));

    $response->assertStatus(200);
    $response->assertViewIs('chapters.index');
    $response->assertViewHas(['chapters', 'books']);

    foreach ($chapters as $chapter) {
        $response->assertSee($chapter->title);
    }
});

test('can filter chapters by book', function () {
    $publisher = Publisher::factory()->create();
    $book1 = Book::factory()->create(['publisher_id' => $publisher->id]);
    $book2 = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapter1 = Chapter::factory()->create(['book_id' => $book1->id, 'title' => 'Chapter in Book 1']);
    $chapter2 = Chapter::factory()->create(['book_id' => $book2->id, 'title' => 'Chapter in Book 2']);

    $response = $this->get(route('chapters.index', ['book_id' => $book1->id]));

    $response->assertStatus(200);
    $response->assertSee('Chapter in Book 1');
    $response->assertDontSee('Chapter in Book 2');
});

test('can view chapter create form', function () {
    $publisher = Publisher::factory()->create();
    Book::factory()->create(['publisher_id' => $publisher->id]);

    $response = $this->get(route('chapters.create'));

    $response->assertStatus(200);
    $response->assertViewIs('chapters.create');
    $response->assertViewHas(['books', 'selectedBookId']);
});

test('can create chapter with valid data', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);

    $chapterData = [
        'title' => 'Test Chapter',
        'content' => 'This is the content of the test chapter.',
        'chapter_number' => 1,
        'book_id' => $book->id,
    ];

    $response = $this->post(route('chapters.store'), $chapterData);

    $response->assertRedirect();
    $this->assertDatabaseHas('chapters', $chapterData);

    $chapter = Chapter::where('title', 'Test Chapter')->first();
    $response->assertRedirect(route('chapters.show', $chapter));
});

test('can create chapter with minimal data', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);

    $chapterData = [
        'title' => 'Minimal Chapter',
        'chapter_number' => 1,
        'book_id' => $book->id,
    ];

    $response = $this->post(route('chapters.store'), $chapterData);

    $response->assertRedirect();
    $this->assertDatabaseHas('chapters', $chapterData);
});

test('cannot create chapter without required fields', function () {
    $response = $this->post(route('chapters.store'), []);

    $response->assertSessionHasErrors(['title', 'chapter_number', 'book_id']);
    $this->assertDatabaseCount('chapters', 0);
});

test('cannot create chapter with duplicate chapter number in same book', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    Chapter::factory()->create(['book_id' => $book->id, 'chapter_number' => 1]);

    $chapterData = [
        'title' => 'Duplicate Chapter',
        'chapter_number' => 1,
        'book_id' => $book->id,
    ];

    $response = $this->post(route('chapters.store'), $chapterData);

    $response->assertSessionHasErrors(['chapter_number']);
});

test('can create chapter with same chapter number in different books', function () {
    $publisher = Publisher::factory()->create();
    $book1 = Book::factory()->create(['publisher_id' => $publisher->id]);
    $book2 = Book::factory()->create(['publisher_id' => $publisher->id]);
    Chapter::factory()->create(['book_id' => $book1->id, 'chapter_number' => 1]);

    $chapterData = [
        'title' => 'Chapter in Different Book',
        'chapter_number' => 1,
        'book_id' => $book2->id,
    ];

    $response = $this->post(route('chapters.store'), $chapterData);

    $response->assertRedirect();
    $this->assertDatabaseHas('chapters', $chapterData);
});

test('cannot create chapter with invalid chapter number', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);

    $chapterData = [
        'title' => 'Invalid Chapter',
        'chapter_number' => 0,
        'book_id' => $book->id,
    ];

    $response = $this->post(route('chapters.store'), $chapterData);

    $response->assertSessionHasErrors(['chapter_number']);
    $this->assertDatabaseCount('chapters', 0);
});

test('cannot create chapter with nonexistent book', function () {
    $chapterData = [
        'title' => 'Test Chapter',
        'chapter_number' => 1,
        'book_id' => 999,
    ];

    $response = $this->post(route('chapters.store'), $chapterData);

    $response->assertSessionHasErrors(['book_id']);
    $this->assertDatabaseCount('chapters', 0);
});

test('can view chapter details', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapter = Chapter::factory()->create(['book_id' => $book->id]);

    $response = $this->get(route('chapters.show', $chapter));

    $response->assertStatus(200);
    $response->assertViewIs('chapters.show');
    $response->assertViewHas('chapter', $chapter);
    $response->assertSee($chapter->title);
    $response->assertSee($book->title);
});

test('can view chapter edit form', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapter = Chapter::factory()->create(['book_id' => $book->id]);

    $response = $this->get(route('chapters.edit', $chapter));

    $response->assertStatus(200);
    $response->assertViewIs('chapters.edit');
    $response->assertViewHas(['chapter', 'books']);
});

test('can update chapter with valid data', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapter = Chapter::factory()->create(['book_id' => $book->id, 'chapter_number' => 1]);

    $updateData = [
        'title' => 'Updated Chapter Title',
        'content' => 'Updated chapter content.',
        'chapter_number' => 2,
        'book_id' => $book->id,
    ];

    $response = $this->put(route('chapters.update', $chapter), $updateData);

    $response->assertRedirect(route('chapters.show', $chapter));
    $this->assertDatabaseHas('chapters', array_merge(['id' => $chapter->id], $updateData));
});

test('cannot update chapter without required fields', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapter = Chapter::factory()->create(['book_id' => $book->id]);

    $response = $this->put(route('chapters.update', $chapter), [
        'title' => '',
        'chapter_number' => '',
        'book_id' => '',
    ]);

    $response->assertSessionHasErrors(['title', 'chapter_number', 'book_id']);
});

test('can delete chapter', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapter = Chapter::factory()->create(['book_id' => $book->id]);

    $response = $this->delete(route('chapters.destroy', $chapter));

    $response->assertRedirect(route('chapters.index'));
    $this->assertDatabaseMissing('chapters', ['id' => $chapter->id]);

    // Book should remain
    $this->assertDatabaseHas('books', ['id' => $book->id]);
});

test('unauthenticated user cannot access chapters', function () {
    auth()->logout();

    $response = $this->get(route('chapters.index'));
    $response->assertRedirect(route('login'));
});

test('returns 404 for nonexistent chapter', function () {
    $response = $this->get(route('chapters.show', 999));
    $response->assertStatus(404);
});
