<?php

use App\Models\Book;
use App\Models\Publisher;
use App\Models\User;

beforeEach(function () {
    $this->actingAs(User::factory()->create());
});

test('can view publishers index', function () {
    $publishers = Publisher::factory(3)->create();

    $response = $this->get(route('publishers.index'));

    $response->assertStatus(200);
    $response->assertViewIs('publishers.index');
    $response->assertViewHas('publishers');

    foreach ($publishers as $publisher) {
        $response->assertSee($publisher->name);
    }
});

test('can view publisher create form', function () {
    $response = $this->get(route('publishers.create'));

    $response->assertStatus(200);
    $response->assertViewIs('publishers.create');
});

test('can create publisher with valid data', function () {
    $publisherData = [
        'name' => 'Test Publisher',
        'email' => '<EMAIL>',
        'phone' => '************',
        'website' => 'https://testpublisher.com',
        'address' => '123 Test Street, Test City',
    ];

    $response = $this->post(route('publishers.store'), $publisherData);

    $response->assertRedirect();
    $this->assertDatabaseHas('publishers', $publisherData);

    $publisher = Publisher::where('name', 'Test Publisher')->first();
    $response->assertRedirect(route('publishers.show', $publisher));
});

test('cannot create publisher without required fields', function () {
    $response = $this->post(route('publishers.store'), []);

    $response->assertSessionHasErrors(['name']);
    $this->assertDatabaseCount('publishers', 0);
});

test('cannot create publisher with invalid email', function () {
    $publisherData = [
        'name' => 'Test Publisher',
        'email' => 'invalid-email',
    ];

    $response = $this->post(route('publishers.store'), $publisherData);

    $response->assertSessionHasErrors(['email']);
    $this->assertDatabaseCount('publishers', 0);
});

test('cannot create publisher with invalid website', function () {
    $publisherData = [
        'name' => 'Test Publisher',
        'website' => 'not-a-url',
    ];

    $response = $this->post(route('publishers.store'), $publisherData);

    $response->assertSessionHasErrors(['website']);
    $this->assertDatabaseCount('publishers', 0);
});

test('can view publisher details', function () {
    $publisher = Publisher::factory()->create();

    $response = $this->get(route('publishers.show', $publisher));

    $response->assertStatus(200);
    $response->assertViewIs('publishers.show');
    $response->assertViewHas('publisher', $publisher);
    $response->assertSee($publisher->name);
});

test('can view publisher edit form', function () {
    $publisher = Publisher::factory()->create();

    $response = $this->get(route('publishers.edit', $publisher));

    $response->assertStatus(200);
    $response->assertViewIs('publishers.edit');
    $response->assertViewHas('publisher', $publisher);
});

test('can update publisher with valid data', function () {
    $publisher = Publisher::factory()->create();
    $updateData = [
        'name' => 'Updated Publisher Name',
        'email' => '<EMAIL>',
        'phone' => '************',
        'website' => 'https://updatedpublisher.com',
        'address' => '456 Updated Street, Updated City',
    ];

    $response = $this->put(route('publishers.update', $publisher), $updateData);

    $response->assertRedirect(route('publishers.show', $publisher));
    $this->assertDatabaseHas('publishers', array_merge(['id' => $publisher->id], $updateData));
});

test('cannot update publisher without required fields', function () {
    $publisher = Publisher::factory()->create();

    $response = $this->put(route('publishers.update', $publisher), ['name' => '']);

    $response->assertSessionHasErrors(['name']);
});

test('can delete publisher', function () {
    $publisher = Publisher::factory()->create();

    $response = $this->delete(route('publishers.destroy', $publisher));

    $response->assertRedirect(route('publishers.index'));
    $this->assertDatabaseMissing('publishers', ['id' => $publisher->id]);
});

test('deleting publisher cascades to books', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);

    $this->delete(route('publishers.destroy', $publisher));

    $this->assertDatabaseMissing('publishers', ['id' => $publisher->id]);
    $this->assertDatabaseMissing('books', ['id' => $book->id]);
});

test('publisher shows book count', function () {
    $publisher = Publisher::factory()->create();
    Book::factory(3)->create(['publisher_id' => $publisher->id]);

    $response = $this->get(route('publishers.index'));

    $response->assertSee('3 books');
});

test('unauthenticated user cannot access publishers', function () {
    auth()->logout();

    $response = $this->get(route('publishers.index'));
    $response->assertRedirect(route('login'));
});

test('returns 404 for nonexistent publisher', function () {
    $response = $this->get(route('publishers.show', 999));
    $response->assertStatus(404);
});
