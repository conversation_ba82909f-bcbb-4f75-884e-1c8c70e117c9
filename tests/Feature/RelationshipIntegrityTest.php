<?php

use App\Models\Author;
use App\Models\Book;
use App\Models\Chapter;
use App\Models\Publisher;
use App\Models\User;

beforeEach(function () {
    $this->actingAs(User::factory()->create());
});

// Publisher-Book Relationship Tests
test('deleting publisher with books cascades properly', function () {
    $publisher = Publisher::factory()->create();
    $books = Book::factory(3)->create(['publisher_id' => $publisher->id]);
    $authors = Author::factory(2)->create();

    // Attach authors to books
    foreach ($books as $book) {
        $book->authors()->attach($authors);
    }

    // Create chapters for books
    $chapters = [];
    foreach ($books as $book) {
        $chapters[] = Chapter::factory()->create(['book_id' => $book->id]);
    }

    $this->delete(route('publishers.destroy', $publisher));

    // Publisher should be deleted
    $this->assertDatabaseMissing('publishers', ['id' => $publisher->id]);

    // Books should be deleted (cascade)
    foreach ($books as $book) {
        $this->assertDatabaseMissing('books', ['id' => $book->id]);
    }

    // Chapters should be deleted (cascade)
    foreach ($chapters as $chapter) {
        $this->assertDatabaseMissing('chapters', ['id' => $chapter->id]);
    }

    // Book-Author relationships should be deleted
    $this->assertDatabaseCount('book_author', 0);

    // Authors should remain (not cascaded)
    foreach ($authors as $author) {
        $this->assertDatabaseHas('authors', ['id' => $author->id]);
    }
});

test('cannot delete publisher with books via database constraint', function () {
    $publisher = Publisher::factory()->create();
    Book::factory()->create(['publisher_id' => $publisher->id]);

    // This should work because we have cascade delete set up
    $response = $this->delete(route('publishers.destroy', $publisher));
    $response->assertRedirect(route('publishers.index'));

    $this->assertDatabaseMissing('publishers', ['id' => $publisher->id]);
});

// Book-Author Many-to-Many Relationship Tests
test('deleting book removes author relationships but keeps authors', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $authors = Author::factory(3)->create();
    $book->authors()->attach($authors);

    $this->delete(route('books.destroy', $book));

    // Book should be deleted
    $this->assertDatabaseMissing('books', ['id' => $book->id]);

    // Book-Author relationships should be deleted
    $this->assertDatabaseMissing('book_author', ['book_id' => $book->id]);

    // Authors should remain
    foreach ($authors as $author) {
        $this->assertDatabaseHas('authors', ['id' => $author->id]);
    }
});

test('deleting author removes book relationships but keeps books', function () {
    $publisher = Publisher::factory()->create();
    $books = Book::factory(3)->create(['publisher_id' => $publisher->id]);
    $author = Author::factory()->create();
    $author->books()->attach($books);

    $this->delete(route('authors.destroy', $author));

    // Author should be deleted
    $this->assertDatabaseMissing('authors', ['id' => $author->id]);

    // Book-Author relationships should be deleted
    $this->assertDatabaseMissing('book_author', ['author_id' => $author->id]);

    // Books should remain
    foreach ($books as $book) {
        $this->assertDatabaseHas('books', ['id' => $book->id]);
    }
});

test('book can have multiple authors and author can have multiple books', function () {
    $publisher = Publisher::factory()->create();
    $books = Book::factory(3)->create(['publisher_id' => $publisher->id]);
    $authors = Author::factory(3)->create();

    // Create complex many-to-many relationships
    $books[0]->authors()->attach([$authors[0]->id, $authors[1]->id]);
    $books[1]->authors()->attach([$authors[1]->id, $authors[2]->id]);
    $books[2]->authors()->attach([$authors[0]->id, $authors[2]->id]);

    // Verify relationships
    $this->assertEquals(2, $books[0]->authors()->count());
    $this->assertEquals(2, $books[1]->authors()->count());
    $this->assertEquals(2, $books[2]->authors()->count());

    $this->assertEquals(2, $authors[0]->books()->count());
    $this->assertEquals(2, $authors[1]->books()->count());
    $this->assertEquals(2, $authors[2]->books()->count());
});

// Book-Chapter Relationship Tests
test('deleting book cascades to chapters', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $chapters = Chapter::factory(5)->create(['book_id' => $book->id]);

    $this->delete(route('books.destroy', $book));

    // Book should be deleted
    $this->assertDatabaseMissing('books', ['id' => $book->id]);

    // Chapters should be deleted (cascade)
    foreach ($chapters as $chapter) {
        $this->assertDatabaseMissing('chapters', ['id' => $chapter->id]);
    }
});

test('chapter cannot exist without book', function () {
    // Try to create chapter with non-existent book
    $chapterData = [
        'title' => 'Orphan Chapter',
        'chapter_number' => 1,
        'book_id' => 999,
    ];

    $response = $this->post(route('chapters.store'), $chapterData);
    $response->assertSessionHasErrors(['book_id']);
});

// Unique Constraint Tests
test('chapter numbers must be unique within same book', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    Chapter::factory()->create(['book_id' => $book->id, 'chapter_number' => 1]);

    $duplicateChapterData = [
        'title' => 'Duplicate Chapter',
        'chapter_number' => 1,
        'book_id' => $book->id,
    ];

    $response = $this->post(route('chapters.store'), $duplicateChapterData);
    $response->assertSessionHasErrors(['chapter_number']);
});

test('isbn must be unique across all books', function () {
    $publisher = Publisher::factory()->create();
    Book::factory()->create(['isbn' => '978-0123456789', 'publisher_id' => $publisher->id]);

    $duplicateBookData = [
        'title' => 'Duplicate ISBN Book',
        'isbn' => '978-0123456789',
        'publisher_id' => $publisher->id,
    ];

    $response = $this->post(route('books.store'), $duplicateBookData);
    $response->assertSessionHasErrors(['isbn']);
});

// Edge Cases
test('book can exist without authors', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);

    $this->assertEquals(0, $book->authors()->count());

    $response = $this->get(route('books.show', $book));
    $response->assertStatus(200);
    $response->assertSee('No authors');
});

test('book can exist without chapters', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);

    $this->assertEquals(0, $book->chapters()->count());

    $response = $this->get(route('books.show', $book));
    $response->assertStatus(200);
    $response->assertSee('No chapters');
});

test('author can exist without books', function () {
    $author = Author::factory()->create();

    $this->assertEquals(0, $author->books()->count());

    $response = $this->get(route('authors.show', $author));
    $response->assertStatus(200);
    $response->assertSee('No books');
});

test('publisher can exist without books', function () {
    $publisher = Publisher::factory()->create();

    $this->assertEquals(0, $publisher->books()->count());

    $response = $this->get(route('publishers.show', $publisher));
    $response->assertStatus(200);
    $response->assertSee('No books');
});

// Complex Scenario Tests
test('complex deletion scenario maintains data integrity', function () {
    // Create a complex scenario
    $publisher1 = Publisher::factory()->create();
    $publisher2 = Publisher::factory()->create();

    $book1 = Book::factory()->create(['publisher_id' => $publisher1->id]);
    $book2 = Book::factory()->create(['publisher_id' => $publisher1->id]);
    $book3 = Book::factory()->create(['publisher_id' => $publisher2->id]);

    $author1 = Author::factory()->create();
    $author2 = Author::factory()->create();

    // Complex relationships
    $book1->authors()->attach([$author1->id, $author2->id]);
    $book2->authors()->attach([$author1->id]);
    $book3->authors()->attach([$author2->id]);

    $chapter1 = Chapter::factory()->create(['book_id' => $book1->id]);
    $chapter2 = Chapter::factory()->create(['book_id' => $book2->id]);
    $chapter3 = Chapter::factory()->create(['book_id' => $book3->id]);

    // Delete publisher1 (should cascade to book1 and book2, but not book3)
    $this->delete(route('publishers.destroy', $publisher1));

    // Verify cascading deletions
    $this->assertDatabaseMissing('publishers', ['id' => $publisher1->id]);
    $this->assertDatabaseMissing('books', ['id' => $book1->id]);
    $this->assertDatabaseMissing('books', ['id' => $book2->id]);
    $this->assertDatabaseMissing('chapters', ['id' => $chapter1->id]);
    $this->assertDatabaseMissing('chapters', ['id' => $chapter2->id]);

    // Verify what should remain
    $this->assertDatabaseHas('publishers', ['id' => $publisher2->id]);
    $this->assertDatabaseHas('books', ['id' => $book3->id]);
    $this->assertDatabaseHas('chapters', ['id' => $chapter3->id]);
    $this->assertDatabaseHas('authors', ['id' => $author1->id]);
    $this->assertDatabaseHas('authors', ['id' => $author2->id]);

    // Verify relationship cleanup
    $this->assertDatabaseMissing('book_author', ['book_id' => $book1->id]);
    $this->assertDatabaseMissing('book_author', ['book_id' => $book2->id]);
    $this->assertDatabaseHas('book_author', ['book_id' => $book3->id, 'author_id' => $author2->id]);
});

test('updating book authors syncs relationships correctly', function () {
    $publisher = Publisher::factory()->create();
    $book = Book::factory()->create(['publisher_id' => $publisher->id]);
    $oldAuthors = Author::factory(2)->create();
    $newAuthors = Author::factory(2)->create();

    // Initially attach old authors
    $book->authors()->attach($oldAuthors);
    $this->assertEquals(2, $book->authors()->count());

    // Update book with new authors
    $updateData = [
        'title' => $book->title,
        'publisher_id' => $publisher->id,
        'authors' => $newAuthors->pluck('id')->toArray(),
    ];

    $response = $this->put(route('books.update', $book), $updateData);
    $response->assertRedirect();

    // Verify sync worked correctly
    $book->refresh();
    $this->assertEquals(2, $book->authors()->count());

    foreach ($newAuthors as $author) {
        $this->assertTrue($book->authors->contains($author));
    }

    foreach ($oldAuthors as $author) {
        $this->assertFalse($book->authors->contains($author));
    }

    // Verify old relationships were removed from pivot table
    foreach ($oldAuthors as $author) {
        $this->assertDatabaseMissing('book_author', [
            'book_id' => $book->id,
            'author_id' => $author->id,
        ]);
    }
});
