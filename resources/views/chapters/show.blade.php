<x-layouts.app :title="$chapter->title">
    <div class="flex h-full w-full flex-1 flex-col gap-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $chapter->title }}</h1>
                <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Chapter {{ $chapter->chapter_number }} of
                    <a href="{{ route('books.show', $chapter->book) }}" class="text-blue-600 hover:underline dark:text-blue-400">
                        {{ $chapter->book->title }}
                    </a>
                </div>
            </div>
            <div class="flex items-center gap-2">
                <a href="{{ route('chapters.edit', $chapter) }}"
                   class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Edit Chapter
                </a>
                <a href="{{ route('chapters.index') }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to Chapters
                </a>
            </div>
        </div>

        @if(session('success'))
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                {{ session('success') }}
            </div>
        @endif

        <div class="grid gap-6 md:grid-cols-3">
            <!-- Chapter Details -->
            <div class="md:col-span-2">
                <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Chapter Content</h2>

                    @if($chapter->content)
                        <div class="prose prose-sm max-w-none dark:prose-invert">
                            <div class="text-gray-900 dark:text-white whitespace-pre-line">{{ $chapter->content }}</div>
                        </div>
                    @else
                        <div class="text-gray-500 dark:text-gray-400 italic">
                            No content available for this chapter.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Chapter Info -->
                <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Chapter Info</h2>

                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Chapter Number</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $chapter->chapter_number }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Title</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $chapter->title }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Book</dt>
                            <dd class="text-sm">
                                <a href="{{ route('books.show', $chapter->book) }}" class="text-blue-600 hover:underline dark:text-blue-400">
                                    {{ $chapter->book->title }}
                                </a>
                            </dd>
                        </div>

                        @if($chapter->book->publisher)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Publisher</dt>
                                <dd class="text-sm">
                                    <a href="{{ route('publishers.show', $chapter->book->publisher) }}" class="text-blue-600 hover:underline dark:text-blue-400">
                                        {{ $chapter->book->publisher->name }}
                                    </a>
                                </dd>
                            </div>
                        @endif

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $chapter->created_at->format('M j, Y') }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $chapter->updated_at->format('M j, Y') }}</dd>
                        </div>
                    </dl>
                </div>

                <!-- Quick Actions -->
                <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>

                    <div class="space-y-2">
                        <a href="{{ route('books.show', $chapter->book) }}"
                           class="block w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors">
                            View Book
                        </a>
                        <a href="{{ route('chapters.create', ['book_id' => $chapter->book_id]) }}"
                           class="block w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors">
                            Add Another Chapter
                        </a>
                        <a href="{{ route('chapters.index', ['book_id' => $chapter->book_id]) }}"
                           class="block w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors">
                            View All Chapters in Book
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
