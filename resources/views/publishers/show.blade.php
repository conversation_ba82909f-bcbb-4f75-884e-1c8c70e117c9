<x-layouts.app :title="$publisher->name">
    <div class="flex h-full w-full flex-1 flex-col gap-4">
        <div class="flex items-center justify-between">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $publisher->name }}</h1>
            <div class="flex items-center gap-2">
                <a href="{{ route('publishers.edit', $publisher) }}"
                   class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Edit Publisher
                </a>
                <a href="{{ route('publishers.index') }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to Publishers
                </a>
            </div>
        </div>

        @if(session('success'))
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                {{ session('success') }}
            </div>
        @endif

        <div class="grid gap-6 md:grid-cols-2">
            <!-- Publisher Details -->
            <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Publisher Details</h2>

                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                        <dd class="text-sm text-gray-900 dark:text-white">{{ $publisher->name }}</dd>
                    </div>

                    @if($publisher->email)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                            <dd class="text-sm">
                                <a href="mailto:{{ $publisher->email }}" class="text-blue-600 hover:underline dark:text-blue-400">
                                    {{ $publisher->email }}
                                </a>
                            </dd>
                        </div>
                    @endif

                    @if($publisher->phone)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $publisher->phone }}</dd>
                        </div>
                    @endif

                    @if($publisher->website)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Website</dt>
                            <dd class="text-sm">
                                <a href="{{ $publisher->website }}" target="_blank" class="text-blue-600 hover:underline dark:text-blue-400">
                                    {{ $publisher->website }}
                                </a>
                            </dd>
                        </div>
                    @endif

                    @if($publisher->address)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</dt>
                            <dd class="text-sm text-gray-900 dark:text-white whitespace-pre-line">{{ $publisher->address }}</dd>
                        </div>
                    @endif
                </dl>
            </div>

            <!-- Statistics -->
            <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Statistics</h2>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Total Books</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            {{ $publisher->books->count() }}
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Created</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $publisher->created_at->format('M j, Y') }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Last Updated</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $publisher->updated_at->format('M j, Y') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Books -->
        @if($publisher->books->count() > 0)
            <div class="rounded-xl border border-neutral-200 dark:border-neutral-700">
                <div class="p-6 border-b border-neutral-200 dark:border-neutral-700">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Published Books</h2>
                        <a href="{{ route('books.create', ['publisher_id' => $publisher->id]) }}"
                           class="inline-flex items-center px-3 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            Add Book
                        </a>
                    </div>
                </div>

                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Authors</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Publication Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($publisher->books as $book)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="font-medium text-gray-900 dark:text-white">{{ $book->title }}</div>
                                    @if($book->isbn)
                                        <div class="text-sm text-gray-500 dark:text-gray-400">ISBN: {{ $book->isbn }}</div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($book->authors->count() > 0)
                                        <div class="text-sm text-gray-900 dark:text-white">
                                            {{ $book->authors->pluck('name')->join(', ') }}
                                        </div>
                                    @else
                                        <span class="text-gray-400">No authors</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-gray-900 dark:text-white">
                                    {{ $book->publication_date ? $book->publication_date->format('M j, Y') : '—' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <a href="{{ route('books.show', $book) }}"
                                       class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                        View
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-8 text-center">
                <div class="text-gray-500 dark:text-gray-400">
                    No books published yet.
                    <a href="{{ route('books.create', ['publisher_id' => $publisher->id]) }}"
                       class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 font-medium ml-1">
                        Add the first book
                    </a>
                </div>
            </div>
        @endif
    </div>
</x-layouts.app>
