<x-layouts.app :title="$book->title">
    <div class="flex h-full w-full flex-1 flex-col gap-4">
        <div class="flex items-center justify-between">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $book->title }}</h1>
            <div class="flex items-center gap-2">
                <a href="{{ route('books.edit', $book) }}"
                   class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Edit Book
                </a>
                <a href="{{ route('books.index') }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to Books
                </a>
            </div>
        </div>

        @if(session('success'))
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                {{ session('success') }}
            </div>
        @endif

        <div class="grid gap-6 md:grid-cols-2">
            <!-- Book Details -->
            <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Book Details</h2>

                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Title</dt>
                        <dd class="text-sm text-gray-900 dark:text-white">{{ $book->title }}</dd>
                    </div>

                    @if($book->isbn)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">ISBN</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $book->isbn }}</dd>
                        </div>
                    @endif

                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Publisher</dt>
                        <dd class="text-sm">
                            @if($book->publisher)
                                <a href="{{ route('publishers.show', $book->publisher) }}" class="text-blue-600 hover:underline dark:text-blue-400">
                                    {{ $book->publisher->name }}
                                </a>
                            @else
                                <span class="text-gray-400">No publisher</span>
                            @endif
                        </dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Authors</dt>
                        <dd class="text-sm">
                            @if($book->authors->count() > 0)
                                @foreach($book->authors as $author)
                                    <a href="{{ route('authors.show', $author) }}" class="text-blue-600 hover:underline dark:text-blue-400">
                                        {{ $author->name }}
                                    </a>@if(!$loop->last), @endif
                                @endforeach
                            @else
                                <span class="text-gray-400">No authors</span>
                            @endif
                        </dd>
                    </div>

                    @if($book->publication_date)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Publication Date</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $book->publication_date->format('F j, Y') }}</dd>
                        </div>
                    @endif

                    @if($book->description)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                            <dd class="text-sm text-gray-900 dark:text-white whitespace-pre-line">{{ $book->description }}</dd>
                        </div>
                    @endif
                </dl>
            </div>

            <!-- Statistics -->
            <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Statistics</h2>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Total Chapters</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            {{ $book->chapters->count() }}
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Authors</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            {{ $book->authors->count() }}
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Created</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $book->created_at->format('M j, Y') }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Last Updated</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $book->updated_at->format('M j, Y') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chapters -->
        @if($book->chapters->count() > 0)
            <div class="rounded-xl border border-neutral-200 dark:border-neutral-700">
                <div class="p-6 border-b border-neutral-200 dark:border-neutral-700">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Chapters</h2>
                        <a href="{{ route('chapters.create', ['book_id' => $book->id]) }}"
                           class="inline-flex items-center px-3 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            Add Chapter
                        </a>
                    </div>
                </div>

                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Chapter</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($book->chapters as $chapter)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        {{ $chapter->chapter_number }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="font-medium text-gray-900 dark:text-white">{{ $chapter->title }}</div>
                                    @if($chapter->content)
                                        <div class="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                                            {{ Str::limit(strip_tags($chapter->content), 100) }}
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center gap-2">
                                        <a href="{{ route('chapters.show', $chapter) }}"
                                           class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                            View
                                        </a>
                                        <a href="{{ route('chapters.edit', $chapter) }}"
                                           class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm font-medium">
                                            Edit
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="rounded-xl border border-neutral-200 dark:border-neutral-700 p-8 text-center">
                <div class="text-gray-500 dark:text-gray-400">
                    No chapters added yet.
                    <a href="{{ route('chapters.create', ['book_id' => $book->id]) }}"
                       class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 font-medium ml-1">
                        Add the first chapter
                    </a>
                </div>
            </div>
        @endif
    </div>
</x-layouts.app>
