<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreChapterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['nullable', 'string'],
            'chapter_number' => [
                'required',
                'integer',
                'min:1',
                Rule::unique('chapters')->where(function ($query) {
                    return $query->where('book_id', $this->book_id);
                }),
            ],
            'book_id' => ['required', 'exists:books,id'],
        ];
    }
}
