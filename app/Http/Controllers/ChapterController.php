<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreChapterRequest;
use App\Http\Requests\UpdateChapterRequest;
use App\Models\Book;
use App\Models\Chapter;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ChapterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Chapter::with('book');

        if ($request->has('book_id')) {
            $query->where('book_id', $request->book_id);
        }

        $chapters = $query->orderBy('book_id')->orderBy('chapter_number')->paginate(15);
        $books = Book::orderBy('title')->get();

        return view('chapters.index', compact('chapters', 'books'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request): View
    {
        $books = Book::orderBy('title')->get();
        $selectedBookId = $request->get('book_id');

        return view('chapters.create', compact('books', 'selectedBookId'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreChapterRequest $request): RedirectResponse
    {
        $chapter = Chapter::create($request->validated());

        return redirect()->route('chapters.show', $chapter)
            ->with('success', 'Chapter created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Chapter $chapter): View
    {
        $chapter->load('book.publisher');

        return view('chapters.show', compact('chapter'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Chapter $chapter): View
    {
        $books = Book::orderBy('title')->get();
        $chapter->load('book');

        return view('chapters.edit', compact('chapter', 'books'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateChapterRequest $request, Chapter $chapter): RedirectResponse
    {
        $chapter->update($request->validated());

        return redirect()->route('chapters.show', $chapter)
            ->with('success', 'Chapter updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Chapter $chapter): RedirectResponse
    {
        $chapter->delete();

        return redirect()->route('chapters.index')
            ->with('success', 'Chapter deleted successfully.');
    }
}
